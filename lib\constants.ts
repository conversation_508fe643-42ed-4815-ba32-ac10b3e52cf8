// HumanWhisper 应用常量定义

// 应用基础信息
export const APP_CONFIG = {
  name: 'HumanWhisper',
  description: 'AI that explains complex topics in simple, human-friendly language',
  version: '0.1.0',
  author: 'HumanWhisper Team',
  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
} as const

// AI模型配置
export const AI_MODELS = {
  GPT_4_1_NANO: 'gpt-4.1-nano',
  CLAUDE_3_5_HAIKU: 'claude-3-5-haiku-latest',
  GEMINI_2_0_FLASH: 'gemini-2.0-flash',
} as const

// 支持的模型列表 (按积分成本升序排列)
export const SUPPORTED_MODELS = [
  {
    id: AI_MODELS.GPT_4_1_NANO,
    name: 'GPT',
    provider: 'OpenAI',
    description: 'Fast and efficient GPT model, suitable for daily conversations',
    maxTokens: 4096,
    costPer1kTokens: 0.001,
  },
  {
    id: AI_MODELS.GEMINI_2_0_FLASH,
    name: 'Gemini',
    provider: 'Google',
    description: 'Google\'s latest high-speed Gemini model',
    maxTokens: 8192,
    costPer1kTokens: 0.0015,
  },
  {
    id: AI_MODELS.CLAUDE_3_5_HAIKU,
    name: 'Claude',
    provider: 'Anthropic',
    description: 'Anthropic\'s latest fast response model',
    maxTokens: 8192,
    costPer1kTokens: 0.002,
  },
] as const

// 响应长度选项
export const RESPONSE_LENGTHS = {
  BRIEF: 'BRIEF',
  STANDARD: 'STANDARD',
  COMPREHENSIVE: 'COMPREHENSIVE',
  THOROUGH: 'THOROUGH',
} as const

// 响应长度配置
export const RESPONSE_LENGTH_CONFIG = {
  'BRIEF': {
    label: 'Brief Answer',
    description: 'Core points, direct and clear',
    baseTokens: 300,
    maxTokens: 500,
    style: 'concise',
    icon: '⚡',
    complexity: {
      LOW: 300,
      MEDIUM: 400,
      HIGH: 450,
      EXPERT: 500
    }
  },
  'STANDARD': {
    label: 'Standard Answer',
    description: 'Balance detail and conciseness',
    baseTokens: 600,
    maxTokens: 1000,
    style: 'balanced',
    icon: '📝',
    complexity: {
      LOW: 600,
      MEDIUM: 750,
      HIGH: 900,
      EXPERT: 1000
    }
  },
  'COMPREHENSIVE': {
    label: 'Comprehensive Answer',
    description: 'Detailed explanation from multiple angles',
    baseTokens: 1200,
    maxTokens: 2000,
    style: 'detailed',
    icon: '📚',
    complexity: {
      LOW: 1200,
      MEDIUM: 1500,
      HIGH: 1800,
      EXPERT: 2000
    }
  },
  'THOROUGH': {
    label: 'Thorough Answer',
    description: 'Professional depth, comprehensive coverage',
    baseTokens: 2000,
    maxTokens: 3000,
    style: 'expert',
    icon: '🔍',
    complexity: {
      LOW: 2000,
      MEDIUM: 2400,
      HIGH: 2700,
      EXPERT: 3000
    }
  }
} as const

// 目标年龄选项
export const TARGET_AGES = [
  { value: 8, label: '8-year-old child', description: 'Explain in the simplest language' },
  { value: 12, label: '12-year-old student', description: 'Level suitable for middle school students' },
  { value: 16, label: '16-year-old teenager', description: 'Depth that high school students can understand' },
  { value: 18, label: 'Adult', description: 'Professional explanations at adult level' },
] as const

// Supported languages
export const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'zh', name: 'Chinese', nativeName: '中文' },
  { code: 'ja', name: 'Japanese', nativeName: '日本語' },
  { code: 'ko', name: 'Korean', nativeName: '한국어' },
  { code: 'es', name: 'Spanish', nativeName: 'Español' },
  { code: 'fr', name: 'French', nativeName: 'Français' },
  { code: 'de', name: 'German', nativeName: 'Deutsch' },
  { code: 'it', name: 'Italian', nativeName: 'Italiano' },
  { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
  { code: 'ru', name: 'Russian', nativeName: 'Русский' },
] as const

// 默认设置 (使用新的标准响应长度)
export const DEFAULT_SETTINGS = {
  model: AI_MODELS.GPT_4_1_NANO,
  language: 'en',
  responseLength: RESPONSE_LENGTHS.STANDARD, // 使用新的标准长度
  targetAge: 12,
  includeExamples: true,
  useAnalogies: true,
  showThinking: false,
} as const

// API配置
export const API_CONFIG = {
  baseUrl: process.env.AIHUBMIX_BASE_URL || 'https://aihubmix.com/v1',
  appCode: 'PSZA2942', // AIHubMix 10% 优惠码
  timeout: 8000, // 8秒超时，为Vercel Hobby Plan预留缓冲
  retryAttempts: 2, // 减少重试次数以避免超时
  retryDelay: 500, // 减少重试延迟
} as const

// 速率限制
export const RATE_LIMITS = {
  requestsPerMinute: parseInt(process.env.RATE_LIMIT_RPM || '60'),
  requestsPerHour: parseInt(process.env.RATE_LIMIT_RPM || '60') * 60,
  requestsPerDay: parseInt(process.env.RATE_LIMIT_RPM || '60') * 60 * 24,
} as const

// 本地存储键名
export const STORAGE_KEYS = {
  chatHistory: 'humanwhisper_chat_history',
  userSettings: 'humanwhisper_user_settings',
  theme: 'humanwhisper_theme',
  language: 'humanwhisper_language',
} as const

// 主题选项
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
} as const

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection error, please check your network connection',
  API_ERROR: 'AI service is temporarily unavailable, please try again later',
  RATE_LIMIT_ERROR: 'Too many requests, please try again later',
  INVALID_INPUT: 'Invalid input, please re-enter',
  UNKNOWN_ERROR: 'An unknown error occurred, please refresh the page and try again',
} as const

// Success messages
export const SUCCESS_MESSAGES = {
  COPIED_TO_CLIPBOARD: 'Copied to clipboard',
  SETTINGS_SAVED: 'Settings saved',
  MESSAGE_SENT: 'Message sent',
} as const

// 正则表达式
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  URL: /^https?:\/\/.+/,
  PHONE: /^[\+]?[1-9][\d]{0,15}$/,
} as const

// 文件大小限制
export const FILE_LIMITS = {
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
} as const

// 动画持续时间
export const ANIMATION_DURATIONS = {
  fast: 150,
  normal: 300,
  slow: 500,
} as const

// 断点
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const

// 社交媒体链接
export const SOCIAL_LINKS = {
  twitter: 'https://twitter.com/humanwhisper',
  github: 'https://github.com/humanwhisper',
  discord: 'https://discord.gg/humanwhisper',
  email: '<EMAIL>',
} as const

// 功能标志
export const FEATURE_FLAGS = {
  enableAnalytics: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
  enableErrorReporting: process.env.NEXT_PUBLIC_ENABLE_ERROR_REPORTING === 'true',
  enablePromptOptimizer: process.env.NEXT_PUBLIC_ENABLE_PROMPT_OPTIMIZER === 'true',
  enableDebug: process.env.NEXT_PUBLIC_DEBUG === 'true',
} as const
